# YD-RP2040 中文OLED显示使用指南

## 概述
本项目为YD-RP2040开发板提供了在0.96寸OLED屏幕上显示中文字符的完整解决方案。

## 硬件准备

### 需要的硬件
1. YD-RP2040开发板
2. 0.96寸OLED显示屏 (SSD1306驱动，I2C接口)
3. 杜邦线若干

### 连接方式
```
OLED屏幕    YD-RP2040开发板
VCC    →    3.3V
GND    →    GND
SCL    →    GP21 (I2C1 SCL)
SDA    →    GP20 (I2C1 SDA)
```

## 软件安装

### 第一步：准备MicroPython环境
1. 确保YD-RP2040已刷入MicroPython固件
2. 使用Thonny IDE或其他MicroPython开发工具连接设备

### 第二步：上传文件
将以下文件上传到YD-RP2040的根目录：

**必需文件：**
- `ssd1306.py` - 标准SSD1306 OLED驱动
- `chinese_font.py` - 中文字体库
- `oled_chinese.py` - 中文OLED驱动扩展

**测试文件：**
- `quick_chinese_test.py` - 快速测试程序
- `chinese_display_demo.py` - 演示程序
- `test_chinese_display.py` - 完整测试程序

### 第三步：运行测试
在Thonny IDE中运行快速测试：
```python
exec(open('quick_chinese_test.py').read())
```

## 基本使用

### 最简单的例子
```python
import machine
from oled_chinese import ChineseOLED

# 初始化
i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
oled = ChineseOLED(128, 64, i2c)

# 显示中文
oled.fill(0)  # 清屏
oled.chinese_text("你好世界", 0, 0)  # 显示中文
oled.show()  # 刷新显示
```

### 常用功能

#### 1. 显示中文文本
```python
# 在指定位置显示中文
oled.chinese_text("你好世界", 0, 0)

# 居中显示
oled.chinese_text_center("中文测试", 20)

# 中英文混合
oled.chinese_text("Hello 世界", 0, 0)
```

#### 2. 设置字体大小
```python
# 16x16字体 (默认，字大但占空间)
oled.set_chinese_font_size(16)

# 12x12字体 (字小但能显示更多内容)
oled.set_chinese_font_size(12)
```

#### 3. 多行显示
```python
# 多行文本
text = "第一行\n第二行\n第三行"
oled.chinese_text_multiline(text, 0, 0)
```

## 支持的中文字符

当前版本支持以下常用中文字符：
```
你 好 世 界 测 试 显 示 中 文 字 体
温 度 湿 压 力 时 间
```

如需更多字符，可以扩展字体库。

## 实际应用示例

### 示例1：环境监测显示
```python
import machine
import time
from oled_chinese import ChineseOLED

# 初始化
i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
oled = ChineseOLED(128, 64, i2c)

def show_sensor_data():
    # 模拟传感器数据
    temperature = 25.6
    humidity = 65.2
    
    oled.fill(0)
    oled.chinese_text_center("环境监测", 0)
    oled.hline(0, 16, 128, 1)  # 分隔线
    oled.chinese_text(f"温度: {temperature}°C", 5, 22)
    oled.chinese_text(f"湿度: {humidity}%", 5, 38)
    oled.chinese_text(f"时间: {time.time():.0f}s", 5, 54)
    oled.show()

# 循环显示
while True:
    show_sensor_data()
    time.sleep(2)
```

### 示例2：菜单系统
```python
def show_menu():
    menus = ["主菜单", "系统设置", "传感器", "网络配置"]
    selected = 0
    
    oled.fill(0)
    oled.chinese_text_center("系统菜单", 0)
    oled.hline(0, 16, 128, 1)
    
    for i, menu in enumerate(menus):
        prefix = "> " if i == selected else "  "
        y_pos = 20 + i * 12
        oled.chinese_text(f"{prefix}{menu}", 5, y_pos)
    
    oled.show()
```

### 示例3：状态显示
```python
def show_status():
    oled.fill(0)
    oled.chinese_text_center("设备状态", 0)
    oled.chinese_text_center("运行正常", 20)
    oled.chinese_text_center("连接成功", 40)
    oled.show()
```

## 故障排除

### 问题1：屏幕无显示
**可能原因：**
- 硬件连接错误
- I2C地址不匹配
- 电源供应不足

**解决方法：**
1. 检查连接线路
2. 运行I2C扫描代码：
```python
import machine
i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
devices = i2c.scan()
print(f"发现的I2C设备: {[hex(d) for d in devices]}")
```

### 问题2：中文显示为方框
**可能原因：**
- 字符不在字体库中
- 字体数据损坏

**解决方法：**
1. 检查要显示的字符是否在支持列表中
2. 使用以下代码查看支持的字符：
```python
from chinese_font import get_supported_chars
print("支持的字符:", get_supported_chars(16))
```

### 问题3：显示位置错误
**可能原因：**
- 坐标参数错误
- 字体大小设置不当

**解决方法：**
1. 检查坐标范围 (x: 0-127, y: 0-63)
2. 确认字体大小设置正确

## 性能优化建议

1. **减少刷新频率**：避免过于频繁调用`show()`
2. **使用合适的字体大小**：12x12字体更节省空间
3. **局部更新**：只更新变化的区域
4. **内存管理**：定期调用`gc.collect()`清理内存

## 扩展开发

### 添加新的中文字符
如果需要显示更多中文字符，可以：

1. 使用字体工具生成点阵数据
2. 添加到`chinese_font.py`文件中
3. 重新上传文件到设备

### 自定义显示效果
可以基于现有代码开发：
- 滚动文字效果
- 动画显示
- 图形界面
- 数据可视化

## 技术支持

如果遇到问题，可以：
1. 检查硬件连接
2. 运行测试程序诊断
3. 查看错误信息
4. 参考示例代码

## 总结

通过本指南，你应该能够：
1. 正确连接硬件
2. 安装必要的软件文件
3. 运行基本的中文显示功能
4. 开发自己的应用程序

祝你使用愉快！

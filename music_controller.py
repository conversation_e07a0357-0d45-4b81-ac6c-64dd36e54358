"""
音乐播放器控制系统 - YD-RP2040
处理旋转编码器和按钮输入
"""

import machine
import time

class MusicController:
    def __init__(self):
        """初始化控制器"""
        # 旋转编码器引脚
        self.clk_pin = machine.Pin(14, machine.Pin.IN, machine.Pin.PULL_UP)
        self.dt_pin = machine.Pin(15, machine.Pin.IN, machine.Pin.PULL_UP)
        self.sw_pin = machine.Pin(13, machine.Pin.IN, machine.Pin.PULL_UP)
        
        # 按钮引脚（可选）
        self.play_btn = machine.Pin(16, machine.Pin.IN, machine.Pin.PULL_UP)
        self.menu_btn = machine.Pin(17, machine.Pin.IN, machine.Pin.PULL_UP)
        
        # 状态LED
        self.status_led = machine.Pin(25, machine.Pin.OUT)
        
        # 编码器状态
        self.last_clk = self.clk_pin.value()
        self.encoder_pos = 0
        
        # 按钮状态
        self.last_sw_state = True
        self.last_play_state = True
        self.last_menu_state = True
        
        # 防抖计时
        self.last_encoder_time = 0
        self.last_button_time = 0
        
        # 控制模式
        self.control_mode = "SONG"  # SONG, VOLUME, MENU
        
        print("✓ 控制器初始化完成")
    
    def read_encoder(self):
        """读取旋转编码器"""
        current_time = time.ticks_ms()
        
        # 防抖：至少间隔20ms
        if time.ticks_diff(current_time, self.last_encoder_time) < 20:
            return 0
        
        clk_state = self.clk_pin.value()
        dt_state = self.dt_pin.value()
        
        direction = 0
        
        if clk_state != self.last_clk:
            if dt_state != clk_state:
                direction = 1  # 顺时针
                self.encoder_pos += 1
            else:
                direction = -1  # 逆时针
                self.encoder_pos -= 1
            
            self.last_encoder_time = current_time
        
        self.last_clk = clk_state
        return direction
    
    def read_buttons(self):
        """读取所有按钮状态"""
        current_time = time.ticks_ms()
        
        # 防抖：至少间隔200ms
        if time.ticks_diff(current_time, self.last_button_time) < 200:
            return {}
        
        buttons = {}
        
        # 编码器按钮
        sw_state = self.sw_pin.value()
        if not sw_state and self.last_sw_state:
            buttons['encoder'] = True
            self.last_button_time = current_time
        self.last_sw_state = sw_state
        
        # 播放按钮
        play_state = self.play_btn.value()
        if not play_state and self.last_play_state:
            buttons['play'] = True
            self.last_button_time = current_time
        self.last_play_state = play_state
        
        # 菜单按钮
        menu_state = self.menu_btn.value()
        if not menu_state and self.last_menu_state:
            buttons['menu'] = True
            self.last_button_time = current_time
        self.last_menu_state = menu_state
        
        return buttons
    
    def set_led_status(self, status):
        """设置状态LED"""
        if status == "playing":
            self.status_led.on()
        elif status == "paused":
            self.status_led.off()
        elif status == "blink":
            # 闪烁效果
            for _ in range(3):
                self.status_led.on()
                time.sleep(0.1)
                self.status_led.off()
                time.sleep(0.1)
    
    def get_control_mode(self):
        """获取当前控制模式"""
        return self.control_mode
    
    def set_control_mode(self, mode):
        """设置控制模式"""
        if mode in ["SONG", "VOLUME", "MENU"]:
            self.control_mode = mode
            print(f"控制模式切换到: {mode}")
            self.set_led_status("blink")
    
    def process_input(self):
        """处理所有输入，返回控制命令"""
        commands = []
        
        # 读取编码器
        encoder_dir = self.read_encoder()
        if encoder_dir != 0:
            if self.control_mode == "SONG":
                if encoder_dir > 0:
                    commands.append("next_song")
                else:
                    commands.append("prev_song")
            elif self.control_mode == "VOLUME":
                if encoder_dir > 0:
                    commands.append("volume_up")
                else:
                    commands.append("volume_down")
            elif self.control_mode == "MENU":
                if encoder_dir > 0:
                    commands.append("menu_down")
                else:
                    commands.append("menu_up")
        
        # 读取按钮
        buttons = self.read_buttons()
        
        if 'encoder' in buttons:
            if self.control_mode == "SONG":
                commands.append("play_pause")
            elif self.control_mode == "VOLUME":
                commands.append("volume_confirm")
            elif self.control_mode == "MENU":
                commands.append("menu_select")
        
        if 'play' in buttons:
            commands.append("play_pause")
        
        if 'menu' in buttons:
            # 循环切换控制模式
            modes = ["SONG", "VOLUME", "MENU"]
            current_idx = modes.index(self.control_mode)
            next_idx = (current_idx + 1) % len(modes)
            self.set_control_mode(modes[next_idx])
            commands.append("mode_changed")
        
        return commands

class InputHandler:
    """输入处理器 - 简化版本，不需要额外硬件"""
    def __init__(self):
        self.encoder_pos = 0
        self.last_time = 0
        
    def simulate_input(self):
        """模拟输入（用于测试）"""
        import random
        
        current_time = time.ticks_ms()
        if time.ticks_diff(current_time, self.last_time) > 2000:  # 每2秒模拟一次输入
            self.last_time = current_time
            
            # 随机生成命令
            commands = ["next_song", "prev_song", "play_pause", "volume_up", "volume_down"]
            return [random.choice(commands)]
        
        return []

# 测试代码
if __name__ == "__main__":
    controller = MusicController()
    
    print("音乐控制器测试")
    print("操作说明:")
    print("- 旋转编码器: 切换歌曲")
    print("- 按下编码器: 播放/暂停")
    print("- 按播放按钮: 播放/暂停")
    print("- 按菜单按钮: 切换控制模式")
    print("按Ctrl+C退出测试")
    
    try:
        while True:
            commands = controller.process_input()
            
            if commands:
                print(f"控制命令: {commands}")
                print(f"当前模式: {controller.get_control_mode()}")
                
                # 根据命令设置LED状态
                if "play_pause" in commands:
                    controller.set_led_status("blink")
                elif "next_song" in commands or "prev_song" in commands:
                    controller.set_led_status("playing")
            
            time.sleep(0.05)  # 50ms轮询间隔
            
    except KeyboardInterrupt:
        controller.status_led.off()
        print("\n控制器测试结束")

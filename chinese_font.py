"""
中文字体支持模块 - YD-RP2040 OLED显示
支持常用中文字符的点阵字体
"""

# 16x16点阵中文字体数据 (部分常用字符)
# 每个字符用32个字节表示 (16x16 = 256位 = 32字节)
CHINESE_FONT_16x16 = {
    # "你" 字的点阵数据
    "你": [
        0x00, 0x00, 0x00, 0x00, 0x10, 0x40, 0x10, 0x40,
        0x10, 0x40, 0x10, 0x40, 0xFF, 0xFE, 0x10, 0x40,
        0x10, 0x40, 0x10, 0x40, 0x10, 0x40, 0x1F, 0xF0,
        0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x0F, 0xE0
    ],
    
    # "好" 字的点阵数据
    "好": [
        0x00, 0x00, 0x00, 0x00, 0x20, 0x08, 0x20, 0x08,
        0x20, 0x08, 0x20, 0x08, 0x3F, 0xF8, 0x20, 0x08,
        0x20, 0x08, 0x3F, 0xF8, 0x20, 0x08, 0x20, 0x08,
        0x20, 0x08, 0x20, 0x08, 0x20, 0x08, 0x00, 0x00
    ],
    
    # "世" 字的点阵数据
    "世": [
        0x00, 0x00, 0x00, 0x00, 0x04, 0x40, 0x04, 0x40,
        0x04, 0x40, 0x04, 0x40, 0x04, 0x40, 0xFF, 0xFE,
        0x04, 0x40, 0x04, 0x40, 0x04, 0x40, 0x04, 0x40,
        0x04, 0x40, 0x04, 0x40, 0x04, 0x40, 0x00, 0x00
    ],
    
    # "界" 字的点阵数据
    "界": [
        0x00, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x10, 0x10,
        0x10, 0x10, 0x1F, 0xF0, 0x10, 0x10, 0x10, 0x10,
        0x1F, 0xF0, 0x04, 0x40, 0x04, 0x40, 0xFF, 0xFE,
        0x04, 0x40, 0x04, 0x40, 0x04, 0x40, 0x00, 0x00
    ],
    
    # "测" 字的点阵数据
    "测": [
        0x00, 0x00, 0x00, 0x00, 0x40, 0x04, 0x40, 0x04,
        0x40, 0x04, 0x7F, 0xFC, 0x40, 0x04, 0x40, 0x04,
        0x40, 0x04, 0x5F, 0xF4, 0x50, 0x14, 0x50, 0x14,
        0x50, 0x14, 0x5F, 0xF4, 0x50, 0x04, 0x00, 0x00
    ],
    
    # "试" 字的点阵数据
    "试": [
        0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x47, 0xF8,
        0x44, 0x08, 0x44, 0x08, 0x47, 0xF8, 0x44, 0x08,
        0x44, 0x08, 0x47, 0xF8, 0x40, 0x08, 0x40, 0x08,
        0x40, 0x08, 0x7F, 0xFE, 0x40, 0x00, 0x00, 0x00
    ],
    
    # "显" 字的点阵数据
    "显": [
        0x00, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x10, 0x10,
        0x10, 0x10, 0x1F, 0xF0, 0x10, 0x10, 0x10, 0x10,
        0x1F, 0xF0, 0x01, 0x00, 0x01, 0x00, 0xFF, 0xFE,
        0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00
    ],
    
    # "示" 字的点阵数据
    "示": [
        0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x80,
        0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0xFF, 0xFE,
        0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80,
        0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00
    ],
    
    # "中" 字的点阵数据
    "中": [
        0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x3F, 0xF8, 0x21, 0x08,
        0x21, 0x08, 0x21, 0x08, 0x3F, 0xF8, 0x01, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00
    ],
    
    # "文" 字的点阵数据
    "文": [
        0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x01, 0x00,
        0x01, 0x00, 0xFF, 0xFE, 0x01, 0x00, 0x01, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00
    ],
    
    # "字" 字的点阵数据
    "字": [
        0x00, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x10, 0x10,
        0x10, 0x10, 0x10, 0x10, 0x1F, 0xF0, 0x00, 0x00,
        0x00, 0x00, 0xFF, 0xFE, 0x01, 0x00, 0x01, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00
    ],
    
    # "体" 字的点阵数据
    "体": [
        0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x20, 0x00,
        0x20, 0x00, 0x3F, 0xFC, 0x20, 0x04, 0x20, 0x04,
        0x20, 0x04, 0x3F, 0xFC, 0x20, 0x04, 0x20, 0x04,
        0x20, 0x04, 0x20, 0x04, 0x20, 0x00, 0x00, 0x00
    ],
    
    # "温" 字的点阵数据
    "温": [
        0x00, 0x00, 0x00, 0x00, 0x40, 0x04, 0x47, 0xF4,
        0x44, 0x44, 0x44, 0x44, 0x47, 0xF4, 0x44, 0x44,
        0x44, 0x44, 0x47, 0xF4, 0x40, 0x04, 0x5F, 0xF4,
        0x51, 0x14, 0x51, 0x14, 0x5F, 0xF4, 0x00, 0x00
    ],
    
    # "度" 字的点阵数据
    "度": [
        0x00, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x10, 0x10,
        0x10, 0x10, 0x1F, 0xF0, 0x10, 0x10, 0x10, 0x10,
        0x1F, 0xF0, 0x00, 0x10, 0x00, 0x10, 0x7F, 0xFE,
        0x40, 0x02, 0x40, 0x02, 0x7F, 0xFE, 0x00, 0x00
    ],
    
    # "湿" 字的点阵数据
    "湿": [
        0x00, 0x00, 0x00, 0x00, 0x40, 0x04, 0x47, 0xF4,
        0x44, 0x44, 0x44, 0x44, 0x47, 0xF4, 0x44, 0x44,
        0x44, 0x44, 0x47, 0xF4, 0x40, 0x04, 0x4F, 0xE4,
        0x48, 0x24, 0x48, 0x24, 0x4F, 0xE4, 0x00, 0x00
    ],
    
    # "压" 字的点阵数据
    "压": [
        0x00, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x10, 0x10,
        0x10, 0x10, 0x10, 0x10, 0x1F, 0xF0, 0x10, 0x10,
        0x10, 0x10, 0x10, 0x10, 0x1F, 0xF0, 0x00, 0x00,
        0x00, 0x00, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0x00
    ],
    
    # "力" 字的点阵数据
    "力": [
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC,
        0x00, 0x04, 0x00, 0x04, 0x00, 0x04, 0x00, 0x04,
        0x00, 0x04, 0x00, 0x04, 0x00, 0x04, 0x00, 0x04,
        0x00, 0x04, 0x00, 0x04, 0x00, 0x04, 0x00, 0x00
    ],
    
    # "时" 字的点阵数据
    "时": [
        0x00, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x10, 0x10,
        0x10, 0x10, 0x1F, 0xF0, 0x10, 0x10, 0x10, 0x10,
        0x1F, 0xF0, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10,
        0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x00
    ],
    
    # "间" 字的点阵数据
    "间": [
        0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x20, 0x08,
        0x20, 0x08, 0x20, 0x08, 0x3F, 0xF8, 0x20, 0x08,
        0x20, 0x08, 0x20, 0x08, 0x3F, 0xF8, 0x01, 0x00,
        0x01, 0x00, 0xFF, 0xFE, 0x01, 0x00, 0x00, 0x00
    ]
}

# 12x12点阵中文字体数据 (节省空间的小字体)
CHINESE_FONT_12x12 = {
    # "你" 字的12x12点阵
    "你": [
        0x00, 0x00, 0x08, 0x20, 0x08, 0x20, 0xFF, 0xF0,
        0x08, 0x20, 0x08, 0x20, 0x0F, 0xE0, 0x08, 0x20,
        0x08, 0x20, 0x08, 0x20, 0x07, 0xC0, 0x00, 0x00
    ],
    
    # "好" 字的12x12点阵
    "好": [
        0x00, 0x00, 0x20, 0x40, 0x20, 0x40, 0x3F, 0xC0,
        0x20, 0x40, 0x3F, 0xC0, 0x20, 0x40, 0x20, 0x40,
        0x20, 0x40, 0x20, 0x40, 0x20, 0x40, 0x00, 0x00
    ]
}

def get_chinese_char_data(char, font_size=16):
    """
    获取中文字符的点阵数据
    
    Args:
        char: 中文字符
        font_size: 字体大小 (12 或 16)
    
    Returns:
        字符的点阵数据，如果字符不存在则返回None
    """
    if font_size == 16:
        return CHINESE_FONT_16x16.get(char)
    elif font_size == 12:
        return CHINESE_FONT_12x12.get(char)
    else:
        return None

def get_supported_chars(font_size=16):
    """
    获取支持的中文字符列表
    
    Args:
        font_size: 字体大小 (12 或 16)
    
    Returns:
        支持的字符列表
    """
    if font_size == 16:
        return list(CHINESE_FONT_16x16.keys())
    elif font_size == 12:
        return list(CHINESE_FONT_12x12.keys())
    else:
        return []

def add_chinese_char(char, font_data, font_size=16):
    """
    添加新的中文字符到字体库
    
    Args:
        char: 中文字符
        font_data: 字符的点阵数据
        font_size: 字体大小 (12 或 16)
    """
    if font_size == 16:
        CHINESE_FONT_16x16[char] = font_data
    elif font_size == 12:
        CHINESE_FONT_12x12[char] = font_data

def print_supported_chars():
    """打印所有支持的中文字符"""
    print("支持的16x16中文字符:")
    chars_16 = get_supported_chars(16)
    for i, char in enumerate(chars_16):
        print(char, end=' ')
        if (i + 1) % 10 == 0:
            print()  # 每10个字符换行
    print()
    
    print("\n支持的12x12中文字符:")
    chars_12 = get_supported_chars(12)
    for i, char in enumerate(chars_12):
        print(char, end=' ')
        if (i + 1) % 10 == 0:
            print()
    print()

if __name__ == "__main__":
    print("中文字体库测试")
    print_supported_chars()

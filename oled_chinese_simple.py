"""
简化版中文OLED驱动 - YD-RP2040
基于SSD1306驱动，添加基本中文字符显示功能
"""

import machine
import time
from ssd1306 import SSD1306_I2C
from chinese_font_simple import get_chinese_char_data, get_supported_chars

class ChineseOLED(SSD1306_I2C):
    """支持中文显示的OLED类"""
    
    def __init__(self, width, height, i2c, addr=0x3C, external_vcc=False):
        """初始化中文OLED显示器"""
        super().__init__(width, height, i2c, addr, external_vcc)
        self.chinese_font_size = 16  # 使用16x16字体
    
    def draw_chinese_char(self, char, x, y, color=1):
        """在指定位置绘制中文字符"""
        # 获取字符点阵数据
        char_data = get_chinese_char_data(char, 16)
        
        if char_data is None:
            print(f"不支持的字符: {char}")
            # 显示一个方框表示不支持的字符
            self.rect(x, y, 16, 16, color)
            return False
        
        # 绘制16x16字符
        for row in range(16):
            # 每行2个字节
            byte1 = char_data[row * 2]
            byte2 = char_data[row * 2 + 1]
            
            # 组合成16位数据
            row_data = (byte1 << 8) | byte2
            
            # 绘制每一位
            for col in range(16):
                if row_data & (0x8000 >> col):  # 检查对应位是否为1
                    pixel_x = x + col
                    pixel_y = y + row
                    if 0 <= pixel_x < self.width and 0 <= pixel_y < self.height:
                        self.pixel(pixel_x, pixel_y, color)
        
        return True
    
    def chinese_text(self, text, x, y, color=1):
        """显示中文文本"""
        current_x = x
        success_count = 0
        
        for char in text:
            # 检查是否超出屏幕边界
            if current_x >= self.width:
                break
            
            # 判断是中文还是英文
            if ord(char) > 127:  # 中文字符
                if self.draw_chinese_char(char, current_x, y, color):
                    success_count += 1
                current_x += 16  # 中文字符宽度
            else:  # 英文字符
                # 使用原生text方法显示英文
                self.text(char, current_x, y, color)
                current_x += 8  # 英文字符宽度为8像素
                success_count += 1
        
        return success_count
    
    def chinese_text_center(self, text, y, color=1):
        """居中显示中文文本"""
        # 计算文本总宽度
        total_width = 0
        for char in text:
            if ord(char) > 127:  # 中文字符
                total_width += 16
            else:  # 英文字符
                total_width += 8
        
        # 计算居中的X坐标
        center_x = (self.width - total_width) // 2
        
        # 显示文本
        return self.chinese_text(text, center_x, y, color)

def test_simple_chinese():
    """简单的中文显示测试"""
    print("=== 简单中文OLED测试 ===")
    
    # 初始化I2C
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    
    # 扫描设备
    devices = i2c.scan()
    if not devices:
        print("未发现I2C设备，请检查连接！")
        return False
    
    print(f"发现I2C设备: {[hex(device) for device in devices]}")
    
    # 初始化中文OLED
    try:
        oled = ChineseOLED(128, 64, i2c)
        print("✓ 中文OLED初始化成功")
    except Exception as e:
        print(f"✗ 中文OLED初始化失败: {e}")
        return False
    
    # 测试中文显示
    print("测试中文字符显示...")
    
    # 1. 基本中文显示
    oled.fill(0)
    oled.chinese_text("你好世界", 0, 0)
    oled.show()
    time.sleep(2)
    
    # 2. 居中显示
    oled.fill(0)
    oled.chinese_text_center("中文测试", 20)
    oled.show()
    time.sleep(2)
    
    # 3. 混合文本
    oled.fill(0)
    oled.chinese_text("Hello 世界", 0, 0)
    oled.chinese_text("YD-RP2040", 0, 20)
    oled.chinese_text("显示测试", 0, 40)
    oled.show()
    time.sleep(2)
    
    print("✓ 简单中文OLED测试完成")
    return True

if __name__ == "__main__":
    test_simple_chinese()

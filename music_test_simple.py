"""
音乐播放器简单测试 - 只需要OLED
测试显示功能和音频生成（无需I2S硬件）
"""

import machine
import time
import math
from ssd1306 import SSD1306_I2C

def test_display_only():
    """只测试显示功能"""
    print("=== 音乐播放器显示测试 ===")
    
    # 初始化OLED
    try:
        i2c = machine.I2C(0, scl=machine.Pin(1), sda=machine.Pin(0), freq=400000)
        oled = SSD1306_I2C(128, 64, i2c)
        print("✓ OLED初始化成功")
    except Exception as e:
        print(f"✗ OLED初始化失败: {e}")
        return
    
    # 歌曲列表
    songs = ["小星星", "生日快乐", "欢乐颂", "两只老虎"]
    
    # 显示启动画面
    oled.fill(0)
    oled.text('Music Player', 20, 15)
    oled.text('v1.0', 50, 30)
    oled.text('Loading...', 30, 45)
    oled.show()
    time.sleep(2)
    
    # 模拟播放每首歌
    for i, song in enumerate(songs):
        print(f"🎵 模拟播放: {song}")
        
        # 播放界面动画
        for frame in range(30):  # 3秒动画
            oled.fill(0)
            
            # 标题栏
            oled.text('Music Player', 0, 0)
            oled.hline(0, 8, 128, 1)
            
            # 歌曲信息
            oled.text(song, 0, 12)
            oled.text(f'{i+1}/{len(songs)}', 0, 22)
            
            # 播放状态
            oled.text('Playing...', 0, 32)
            
            # 进度条
            progress = int((frame / 30) * 100)
            oled.text(f'Progress: {progress}%', 0, 42)
            oled.rect(0, 52, 128, 8, 1)
            fill_width = int(126 * progress / 100)
            if fill_width > 0:
                oled.fill_rect(1, 53, fill_width, 6, 1)
            
            # 音符动画
            x_positions = [100, 110, 120]
            for j, x in enumerate(x_positions):
                y_offset = int(3 * math.sin((frame + j * 10) * 0.3))
                y = 25 + y_offset
                oled.pixel(x, y, 1)
                oled.pixel(x+1, y, 1)
            
            oled.show()
            time.sleep(0.1)
        
        # 歌曲间隔
        time.sleep(0.5)
    
    # 结束画面
    oled.fill(0)
    oled.text('Demo Complete!', 15, 25)
    oled.text('Press Ctrl+C', 20, 40)
    oled.show()
    
    print("✓ 显示测试完成")

def test_audio_generation():
    """测试音频数据生成（不需要I2S硬件）"""
    print("\n=== 音频生成测试 ===")
    
    # 音符频率
    notes = {
        'C4': 261.63, 'D4': 293.66, 'E4': 329.63, 'F4': 349.23,
        'G4': 392.00, 'A4': 440.00, 'B4': 493.88, 'C5': 523.25
    }
    
    # 小星星旋律
    melody = [
        ('C4', 0.5), ('C4', 0.5), ('G4', 0.5), ('G4', 0.5),
        ('A4', 0.5), ('A4', 0.5), ('G4', 1.0),
        ('F4', 0.5), ('F4', 0.5), ('E4', 0.5), ('E4', 0.5),
        ('D4', 0.5), ('D4', 0.5), ('C4', 1.0)
    ]
    
    print("🎵 生成音频数据...")
    
    total_samples = 0
    for note, duration in melody:
        if note in notes:
            frequency = notes[note]
            samples = int(22050 * duration)  # 22050Hz采样率
            total_samples += samples
            print(f"音符 {note}: {frequency:.1f}Hz, {duration}秒, {samples}采样点")
    
    print(f"✓ 总计生成 {total_samples} 个采样点")
    print(f"✓ 音频时长约 {total_samples/22050:.1f} 秒")

def test_pwm_audio():
    """测试PWM音频输出（简单版本）"""
    print("\n=== PWM音频测试 ===")
    
    try:
        # 使用PWM生成简单音调
        pwm_pin = machine.PWM(machine.Pin(25))  # 使用板载LED引脚
        
        # 音符频率
        notes = [261, 293, 329, 349, 392, 440, 493, 523]  # C大调音阶
        note_names = ['C', 'D', 'E', 'F', 'G', 'A', 'B', 'C']
        
        print("🎵 PWM音阶演示（LED闪烁频率对应音符频率）...")
        
        for i, (freq, name) in enumerate(zip(notes, note_names)):
            print(f"播放音符: {name} ({freq}Hz)")
            pwm_pin.freq(freq)
            pwm_pin.duty_u16(32768)  # 50%占空比
            time.sleep(0.5)
        
        pwm_pin.deinit()
        print("✓ PWM音频测试完成")
        
    except Exception as e:
        print(f"✗ PWM测试失败: {e}")

def test_control_simulation():
    """模拟控制操作"""
    print("\n=== 控制模拟测试 ===")
    
    # 初始化OLED
    try:
        i2c = machine.I2C(0, scl=machine.Pin(1), sda=machine.Pin(0), freq=400000)
        oled = SSD1306_I2C(128, 64, i2c)
    except:
        print("OLED不可用，跳过控制测试")
        return
    
    songs = ["小星星", "生日快乐", "欢乐颂", "两只老虎"]
    current_song = 0
    volume = 50
    is_playing = False
    
    # 模拟控制操作
    operations = [
        ("播放", "play"),
        ("下一首", "next"),
        ("音量+", "vol_up"),
        ("音量+", "vol_up"),
        ("暂停", "pause"),
        ("上一首", "prev"),
        ("播放", "play"),
        ("音量-", "vol_down")
    ]
    
    for op_name, op_code in operations:
        print(f"🎮 模拟操作: {op_name}")
        
        # 处理操作
        if op_code == "play":
            is_playing = True
        elif op_code == "pause":
            is_playing = False
        elif op_code == "next":
            current_song = (current_song + 1) % len(songs)
        elif op_code == "prev":
            current_song = (current_song - 1) % len(songs)
        elif op_code == "vol_up":
            volume = min(100, volume + 10)
        elif op_code == "vol_down":
            volume = max(0, volume - 10)
        
        # 更新显示
        oled.fill(0)
        
        # 标题
        oled.text('Music Player', 0, 0)
        oled.hline(0, 8, 128, 1)
        
        # 歌曲信息
        oled.text(songs[current_song], 0, 12)
        oled.text(f'{current_song+1}/{len(songs)}', 0, 22)
        
        # 播放状态
        status = "Playing" if is_playing else "Paused"
        oled.text(status, 0, 32)
        
        # 音量
        oled.text(f'Vol: {volume}%', 0, 42)
        oled.rect(30, 44, 80, 6, 1)
        fill_width = int(78 * volume / 100)
        if fill_width > 0:
            oled.fill_rect(31, 45, fill_width, 4, 1)
        
        # 操作提示
        oled.text(f'Op: {op_name}', 0, 54)
        
        oled.show()
        time.sleep(1.5)
    
    print("✓ 控制模拟测试完成")

def main():
    """主测试函数"""
    print("🎵 音乐播放器软件测试")
    print("=" * 40)
    print("这个测试只需要OLED屏幕，无需音频硬件")
    print()
    
    try:
        # 显示功能测试
        test_display_only()
        
        # 音频生成测试
        test_audio_generation()
        
        # PWM音频测试
        test_pwm_audio()
        
        # 控制模拟测试
        test_control_simulation()
        
        print("\n🎉 所有测试完成！")
        print("软件部分工作正常，可以购买音频硬件了！")
        
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n测试出错: {e}")

if __name__ == "__main__":
    main()

"""
支持中文显示的OLED驱动 - YD-RP2040
基于SSD1306驱动，添加中文字符显示功能
"""

import machine
import time
from ssd1306 import SSD1306_I2C
from chinese_font import get_chinese_char_data, get_supported_chars

class ChineseOLED(SSD1306_I2C):
    """支持中文显示的OLED类"""
    
    def __init__(self, width, height, i2c, addr=0x3C, external_vcc=False):
        """
        初始化中文OLED显示器
        
        Args:
            width: 屏幕宽度
            height: 屏幕高度
            i2c: I2C对象
            addr: I2C地址
            external_vcc: 是否使用外部电源
        """
        super().__init__(width, height, i2c, addr, external_vcc)
        self.chinese_font_size = 16  # 默认使用16x16字体
    
    def set_chinese_font_size(self, size):
        """
        设置中文字体大小
        
        Args:
            size: 字体大小 (12 或 16)
        """
        if size in [12, 16]:
            self.chinese_font_size = size
        else:
            print(f"不支持的字体大小: {size}，使用默认16x16")
            self.chinese_font_size = 16
    
    def draw_chinese_char(self, char, x, y, color=1):
        """
        在指定位置绘制中文字符
        
        Args:
            char: 中文字符
            x: X坐标
            y: Y坐标
            color: 颜色 (1=白色, 0=黑色)
        """
        # 获取字符点阵数据
        char_data = get_chinese_char_data(char, self.chinese_font_size)
        
        if char_data is None:
            print(f"不支持的字符: {char}")
            # 显示一个方框表示不支持的字符
            self.rect(x, y, self.chinese_font_size, self.chinese_font_size, color)
            return False
        
        # 绘制字符
        if self.chinese_font_size == 16:
            self._draw_16x16_char(char_data, x, y, color)
        elif self.chinese_font_size == 12:
            self._draw_12x12_char(char_data, x, y, color)
        
        return True
    
    def _draw_16x16_char(self, char_data, x, y, color):
        """绘制16x16字符"""
        for row in range(16):
            # 每行2个字节
            byte1 = char_data[row * 2]
            byte2 = char_data[row * 2 + 1]
            
            # 组合成16位数据
            row_data = (byte1 << 8) | byte2
            
            # 绘制每一位
            for col in range(16):
                if row_data & (0x8000 >> col):  # 检查对应位是否为1
                    pixel_x = x + col
                    pixel_y = y + row
                    if 0 <= pixel_x < self.width and 0 <= pixel_y < self.height:
                        self.pixel(pixel_x, pixel_y, color)
    
    def _draw_12x12_char(self, char_data, x, y, color):
        """绘制12x12字符"""
        for row in range(12):
            # 每行2个字节，但只使用12位
            byte1 = char_data[row * 2]
            byte2 = char_data[row * 2 + 1]
            
            # 组合成16位数据，但只使用高12位
            row_data = (byte1 << 8) | byte2
            
            # 绘制每一位
            for col in range(12):
                if row_data & (0x8000 >> col):  # 检查对应位是否为1
                    pixel_x = x + col
                    pixel_y = y + row
                    if 0 <= pixel_x < self.width and 0 <= pixel_y < self.height:
                        self.pixel(pixel_x, pixel_y, color)
    
    def chinese_text(self, text, x, y, color=1):
        """
        显示中文文本
        
        Args:
            text: 要显示的文本（可包含中文和英文）
            x: 起始X坐标
            y: 起始Y坐标
            color: 颜色 (1=白色, 0=黑色)
        
        Returns:
            显示成功的字符数
        """
        current_x = x
        success_count = 0
        
        for char in text:
            # 检查是否超出屏幕边界
            if current_x >= self.width:
                break
            
            # 判断是中文还是英文
            if ord(char) > 127:  # 中文字符
                if self.draw_chinese_char(char, current_x, y, color):
                    success_count += 1
                current_x += self.chinese_font_size  # 中文字符宽度
            else:  # 英文字符
                # 使用原生text方法显示英文
                self.text(char, current_x, y, color)
                current_x += 8  # 英文字符宽度为8像素
                success_count += 1
        
        return success_count
    
    def chinese_text_center(self, text, y, color=1):
        """
        居中显示中文文本
        
        Args:
            text: 要显示的文本
            y: Y坐标
            color: 颜色
        """
        # 计算文本总宽度
        total_width = 0
        for char in text:
            if ord(char) > 127:  # 中文字符
                total_width += self.chinese_font_size
            else:  # 英文字符
                total_width += 8
        
        # 计算居中的X坐标
        center_x = (self.width - total_width) // 2
        
        # 显示文本
        return self.chinese_text(text, center_x, y, color)
    
    def chinese_text_multiline(self, text, x, y, line_height=None, color=1):
        """
        多行显示中文文本
        
        Args:
            text: 要显示的文本
            x: 起始X坐标
            y: 起始Y坐标
            line_height: 行高，默认为字体大小+2
            color: 颜色
        
        Returns:
            显示的行数
        """
        if line_height is None:
            line_height = self.chinese_font_size + 2
        
        lines = text.split('\n')
        current_y = y
        line_count = 0
        
        for line in lines:
            if current_y + self.chinese_font_size > self.height:
                break  # 超出屏幕高度
            
            self.chinese_text(line, x, current_y, color)
            current_y += line_height
            line_count += 1
        
        return line_count
    
    def show_chinese_demo(self):
        """显示中文演示"""
        print("中文显示演示...")
        
        # 清屏
        self.fill(0)
        
        # 显示标题
        self.chinese_text("中文显示测试", 0, 0)
        
        # 显示支持的字符
        supported_chars = get_supported_chars(self.chinese_font_size)
        demo_text = "".join(supported_chars[:6])  # 显示前6个字符
        self.chinese_text(demo_text, 0, 20)
        
        # 显示混合文本
        self.chinese_text("Hello 世界", 0, 40)
        
        self.show()
        time.sleep(3)
    
    def show_info_display(self):
        """显示信息界面"""
        self.fill(0)
        
        # 标题
        self.chinese_text_center("系统信息", 0)
        
        # 分隔线
        self.hline(0, 18, self.width, 1)
        
        # 信息内容
        import gc
        gc.collect()
        free_mem = gc.mem_free()
        
        self.chinese_text(f"温度: 25°C", 0, 22)
        self.chinese_text(f"湿度: 60%", 0, 38)
        self.chinese_text(f"时间: {time.time():.0f}s", 0, 54)
        
        self.show()

def test_chinese_oled():
    """测试中文OLED显示"""
    print("=== 中文OLED测试 ===")
    
    # 初始化I2C
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    
    # 扫描设备
    devices = i2c.scan()
    if not devices:
        print("未发现I2C设备，请检查连接！")
        return False
    
    print(f"发现I2C设备: {[hex(device) for device in devices]}")
    
    # 初始化中文OLED
    try:
        oled = ChineseOLED(128, 64, i2c)
        print("✓ 中文OLED初始化成功")
    except Exception as e:
        print(f"✗ 中文OLED初始化失败: {e}")
        return False
    
    # 测试中文显示
    print("测试中文字符显示...")
    
    # 1. 基本中文显示
    oled.fill(0)
    oled.chinese_text("你好世界", 0, 0)
    oled.show()
    time.sleep(2)
    
    # 2. 居中显示
    oled.fill(0)
    oled.chinese_text_center("中文测试", 20)
    oled.show()
    time.sleep(2)
    
    # 3. 混合文本
    oled.fill(0)
    oled.chinese_text("Hello 世界", 0, 0)
    oled.chinese_text("YD-RP2040", 0, 20)
    oled.chinese_text("OLED 显示", 0, 40)
    oled.show()
    time.sleep(2)
    
    # 4. 演示界面
    oled.show_chinese_demo()
    
    # 5. 信息显示
    oled.show_info_display()
    time.sleep(3)
    
    print("✓ 中文OLED测试完成")
    return True

if __name__ == "__main__":
    test_chinese_oled()

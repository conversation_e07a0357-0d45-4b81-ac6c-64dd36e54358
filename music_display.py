"""
音乐播放器显示界面 - YD-RP2040
OLED显示歌曲信息和播放状态
"""

import machine
import time
from ssd1306 import SSD1306_I2C

class MusicDisplay:
    def __init__(self):
        """初始化显示器"""
        self.oled = None
        self.current_song = ""
        self.is_playing = False
        self.volume = 50
        self.song_index = 0
        self.total_songs = 0
        self.animation_frame = 0
        
    def init_display(self):
        """初始化OLED显示"""
        try:
            i2c = machine.I2C(0, scl=machine.Pin(1), sda=machine.Pin(0), freq=400000)
            self.oled = SSD1306_I2C(128, 64, i2c)
            print("✓ OLED显示初始化成功")
            return True
        except Exception as e:
            print(f"✗ OLED初始化失败: {e}")
            return False
    
    def clear(self):
        """清屏"""
        if self.oled:
            self.oled.fill(0)
    
    def show(self):
        """刷新显示"""
        if self.oled:
            self.oled.show()
    
    def draw_title_bar(self):
        """绘制标题栏"""
        self.oled.text('Music Player', 0, 0)
        self.oled.hline(0, 8, 128, 1)
    
    def draw_song_info(self):
        """绘制歌曲信息"""
        # 歌曲名称（可能需要滚动显示）
        song_text = self.current_song
        if len(song_text) > 16:  # 如果歌名太长，滚动显示
            scroll_pos = (self.animation_frame // 10) % (len(song_text) - 15)
            song_text = song_text[scroll_pos:scroll_pos + 16]
        
        self.oled.text(song_text, 0, 12)
        
        # 歌曲序号
        song_info = f"{self.song_index + 1}/{self.total_songs}"
        self.oled.text(song_info, 0, 22)
    
    def draw_play_status(self):
        """绘制播放状态"""
        # 播放状态图标
        if self.is_playing:
            # 播放图标（三角形）
            self.oled.fill_rect(10, 35, 3, 8, 1)
            self.oled.fill_rect(13, 37, 3, 4, 1)
            self.oled.fill_rect(16, 39, 3, 2, 1)
            status_text = "Playing"
        else:
            # 暂停图标（两个竖条）
            self.oled.fill_rect(10, 35, 3, 8, 1)
            self.oled.fill_rect(16, 35, 3, 8, 1)
            status_text = "Paused"
        
        self.oled.text(status_text, 25, 35)
    
    def draw_volume_bar(self):
        """绘制音量条"""
        self.oled.text('Vol:', 0, 45)
        
        # 音量条外框
        self.oled.rect(25, 47, 82, 6, 1)
        
        # 音量条填充
        fill_width = int(80 * self.volume / 100)
        if fill_width > 0:
            self.oled.fill_rect(26, 48, fill_width, 4, 1)
        
        # 音量数值
        vol_text = f"{self.volume}%"
        self.oled.text(vol_text, 110, 45)
    
    def draw_animation(self):
        """绘制播放动画"""
        if self.is_playing:
            # 简单的音符动画
            x_positions = [100, 110, 120]
            for i, x in enumerate(x_positions):
                y_offset = int(3 * math.sin((self.animation_frame + i * 10) * 0.2))
                y = 25 + y_offset
                
                # 绘制音符（简化版）
                self.oled.pixel(x, y, 1)
                self.oled.pixel(x+1, y, 1)
                self.oled.pixel(x, y+1, 1)
    
    def draw_progress_bar(self, progress=0):
        """绘制播放进度条"""
        # 进度条（底部）
        self.oled.rect(0, 58, 128, 4, 1)
        if progress > 0:
            fill_width = int(126 * progress / 100)
            self.oled.fill_rect(1, 59, fill_width, 2, 1)
    
    def update_display(self, song_name="", playing=False, volume=50, song_idx=0, total=0, progress=0):
        """更新显示内容"""
        self.current_song = song_name
        self.is_playing = playing
        self.volume = volume
        self.song_index = song_idx
        self.total_songs = total
        
        # 清屏
        self.clear()
        
        # 绘制各个部分
        self.draw_title_bar()
        self.draw_song_info()
        self.draw_play_status()
        self.draw_volume_bar()
        self.draw_progress_bar(progress)
        
        # 如果在播放，绘制动画
        if self.is_playing:
            import math
            # 简单的音符动画
            x_positions = [100, 110, 120]
            for i, x in enumerate(x_positions):
                y_offset = int(2 * math.sin((self.animation_frame + i * 20) * 0.3))
                y = 25 + y_offset
                self.oled.pixel(x, y, 1)
                self.oled.pixel(x+1, y, 1)
        
        # 刷新显示
        self.show()
        
        # 更新动画帧
        self.animation_frame += 1
        if self.animation_frame > 1000:
            self.animation_frame = 0
    
    def show_menu(self, menu_items, selected_index):
        """显示菜单"""
        self.clear()
        
        # 菜单标题
        self.oled.text('Menu', 0, 0)
        self.oled.hline(0, 8, 128, 1)
        
        # 显示菜单项（最多显示5项）
        start_idx = max(0, selected_index - 2)
        end_idx = min(len(menu_items), start_idx + 5)
        
        for i in range(start_idx, end_idx):
            y_pos = 12 + (i - start_idx) * 10
            
            # 选中项前面加箭头
            if i == selected_index:
                self.oled.text('>', 0, y_pos)
                self.oled.text(menu_items[i], 10, y_pos)
            else:
                self.oled.text(menu_items[i], 10, y_pos)
        
        self.show()
    
    def show_volume_adjust(self, volume):
        """显示音量调节界面"""
        self.clear()
        
        # 标题
        self.oled.text('Volume Control', 0, 0)
        self.oled.hline(0, 8, 128, 1)
        
        # 大音量数字
        vol_str = f"{volume}%"
        # 居中显示
        x_pos = (128 - len(vol_str) * 8) // 2
        self.oled.text(vol_str, x_pos, 20)
        
        # 大音量条
        bar_width = 100
        bar_x = (128 - bar_width) // 2
        self.oled.rect(bar_x, 35, bar_width, 10, 1)
        
        fill_width = int((bar_width - 2) * volume / 100)
        if fill_width > 0:
            self.oled.fill_rect(bar_x + 1, 36, fill_width, 8, 1)
        
        # 提示文字
        self.oled.text('Rotate to adjust', 0, 50)
        
        self.show()
    
    def show_startup_screen(self):
        """显示启动画面"""
        self.clear()
        
        # 居中显示标题
        title = "Music Player"
        x_pos = (128 - len(title) * 8) // 2
        self.oled.text(title, x_pos, 15)
        
        # 版本信息
        version = "v1.0"
        x_pos = (128 - len(version) * 8) // 2
        self.oled.text(version, x_pos, 30)
        
        # 启动提示
        hint = "Loading..."
        x_pos = (128 - len(hint) * 8) // 2
        self.oled.text(hint, x_pos, 45)
        
        self.show()

# 测试代码
if __name__ == "__main__":
    display = MusicDisplay()
    
    if display.init_display():
        # 显示启动画面
        display.show_startup_screen()
        time.sleep(2)
        
        # 显示播放界面
        display.update_display(
            song_name="小星星",
            playing=True,
            volume=75,
            song_idx=0,
            total=4,
            progress=30
        )
        
        # 动画演示
        for i in range(50):
            display.update_display(
                song_name="小星星",
                playing=True,
                volume=75,
                song_idx=0,
                total=4,
                progress=(i * 2) % 100
            )
            time.sleep(0.1)
    else:
        print("显示器初始化失败")

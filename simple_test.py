"""
最简单的中文OLED测试 - YD-RP2040
"""

import machine
import time

def main():
    print("YD-RP2040 中文OLED简单测试")
    print("=" * 25)
    
    # 1. 检查I2C连接
    print("1. 检查I2C连接...")
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    devices = i2c.scan()
    
    if devices:
        print(f"✓ 发现设备: {[hex(d) for d in devices]}")
    else:
        print("✗ 未发现设备！请检查连接:")
        print("  OLED VCC → 3.3V")
        print("  OLED GND → GND")
        print("  OLED SCL → GP21")
        print("  OLED SDA → GP20")
        return
    
    # 2. 初始化OLED
    print("2. 初始化OLED...")
    try:
        from oled_chinese_simple import ChineseOLED
        oled = ChineseOLED(128, 64, i2c)
        print("✓ 初始化成功")
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return
    
    # 3. 显示测试
    print("3. 显示测试...")
    
    # 清屏
    oled.fill(0)
    oled.show()
    time.sleep(0.5)
    
    # 显示中文
    oled.chinese_text("你好世界", 0, 0)
    oled.chinese_text("中文显示", 0, 20)
    oled.chinese_text("测试成功", 0, 40)
    oled.show()
    
    print("✓ 如果屏幕显示中文，说明测试成功！")
    
    # 4. 动态演示
    print("4. 动态演示...")
    for i in range(3):
        oled.fill(0)
        oled.chinese_text_center("测试", 10)
        oled.chinese_text_center(f"第{i+1}次", 30)
        oled.show()
        time.sleep(1)
    
    print("✓ 测试完成！")

if __name__ == "__main__":
    main()

"""
YD-RP2040 中文OLED显示演示
简单的中文显示示例
"""

import machine
import time
from oled_chinese import ChineseOLED

def simple_demo():
    """简单的中文显示演示"""
    print("初始化OLED...")
    
    # 配置I2C (SCL=GP21, SDA=GP20)
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    
    # 检查I2C设备
    devices = i2c.scan()
    if not devices:
        print("错误: 未发现OLED设备，请检查连接！")
        return
    
    print(f"发现OLED设备: {hex(devices[0])}")
    
    # 初始化中文OLED (128x64)
    oled = ChineseOLED(128, 64, i2c)
    
    # 演示1: 基本中文显示
    print("演示1: 基本中文显示")
    oled.fill(0)
    oled.chinese_text("你好世界", 0, 0)
    oled.chinese_text("YD-RP2040", 0, 20)
    oled.chinese_text("中文显示", 0, 40)
    oled.show()
    time.sleep(3)
    
    # 演示2: 居中显示
    print("演示2: 居中显示")
    oled.fill(0)
    oled.chinese_text_center("中文测试", 10)
    oled.chinese_text_center("OLED显示", 30)
    oled.chinese_text_center("成功运行", 50)
    oled.show()
    time.sleep(3)
    
    # 演示3: 混合文本
    print("演示3: 中英文混合")
    oled.fill(0)
    oled.chinese_text("Hello 世界", 0, 0)
    oled.chinese_text("Temperature 温度", 0, 20)
    oled.chinese_text("Humidity 湿度", 0, 40)
    oled.show()
    time.sleep(3)
    
    # 演示4: 动态显示
    print("演示4: 动态显示")
    for i in range(5):
        oled.fill(0)
        oled.chinese_text_center("动态显示", 0)
        oled.chinese_text_center(f"计数: {i+1}", 20)
        oled.chinese_text_center(f"时间: {time.time():.0f}s", 40)
        oled.show()
        time.sleep(1)
    
    print("演示完成！")

def sensor_demo():
    """传感器数据显示演示"""
    print("传感器数据显示演示...")
    
    # 初始化OLED
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    oled = ChineseOLED(128, 64, i2c)
    
    # 模拟传感器数据
    import random
    
    for i in range(10):
        # 生成随机数据
        temp = 20 + random.randint(0, 15)
        humidity = 40 + random.randint(0, 40)
        
        oled.fill(0)
        
        # 标题
        oled.chinese_text_center("环境监测", 0)
        oled.hline(0, 16, 128, 1)
        
        # 数据
        oled.chinese_text(f"温度: {temp}°C", 10, 22)
        oled.chinese_text(f"湿度: {humidity}%", 10, 38)
        oled.chinese_text(f"更新: {i+1}", 10, 54)
        
        oled.show()
        time.sleep(2)

def clock_demo():
    """时钟显示演示"""
    print("时钟显示演示...")
    
    # 初始化OLED
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    oled = ChineseOLED(128, 64, i2c)
    
    start_time = time.time()
    
    for i in range(30):  # 运行30秒
        current_time = time.time() - start_time
        hours = int(current_time // 3600)
        minutes = int((current_time % 3600) // 60)
        seconds = int(current_time % 60)
        
        oled.fill(0)
        
        # 标题
        oled.chinese_text_center("数字时钟", 0)
        oled.hline(0, 16, 128, 1)
        
        # 时间显示
        time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        oled.text(time_str, 25, 30)
        
        # 中文标签
        oled.chinese_text("运行时间", 25, 50)
        
        oled.show()
        time.sleep(1)

def menu_demo():
    """菜单显示演示"""
    print("菜单显示演示...")
    
    # 初始化OLED
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    oled = ChineseOLED(128, 64, i2c)
    
    menus = [
        "主菜单",
        "系统设置",
        "传感器",
        "网络配置",
        "关于设备"
    ]
    
    for i, menu in enumerate(menus):
        oled.fill(0)
        
        # 标题
        oled.chinese_text_center("系统菜单", 0)
        oled.hline(0, 16, 128, 1)
        
        # 当前菜单项
        oled.chinese_text_center(f"> {menu}", 25)
        
        # 页码
        oled.text(f"{i+1}/{len(menus)}", 50, 50)
        
        oled.show()
        time.sleep(2)

def main():
    """主函数"""
    print("YD-RP2040 中文OLED显示演示")
    print("=" * 30)
    print("硬件连接:")
    print("OLED VCC → 3.3V")
    print("OLED GND → GND")
    print("OLED SCL → GP21")
    print("OLED SDA → GP20")
    print()
    
    try:
        # 运行各种演示
        simple_demo()
        time.sleep(1)
        
        sensor_demo()
        time.sleep(1)
        
        clock_demo()
        time.sleep(1)
        
        menu_demo()
        
        print("所有演示完成！")
        
    except Exception as e:
        print(f"演示过程中出错: {e}")
        print("请检查硬件连接和代码")

if __name__ == "__main__":
    main()

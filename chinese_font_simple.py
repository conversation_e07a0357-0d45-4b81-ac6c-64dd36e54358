"""
简化版中文字体库 - YD-RP2040 OLED显示
包含最基本的中文字符
"""

# 16x16点阵中文字体数据 (基本字符)
CHINESE_FONT_16x16 = {
    # "你" 字
    "你": [
        0x00, 0x00, 0x00, 0x00, 0x10, 0x40, 0x10, 0x40,
        0x10, 0x40, 0x10, 0x40, 0xFF, 0xFE, 0x10, 0x40,
        0x10, 0x40, 0x10, 0x40, 0x10, 0x40, 0x1F, 0xF0,
        0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x0F, 0xE0
    ],
    
    # "好" 字
    "好": [
        0x00, 0x00, 0x00, 0x00, 0x20, 0x08, 0x20, 0x08,
        0x20, 0x08, 0x20, 0x08, 0x3F, 0xF8, 0x20, 0x08,
        0x20, 0x08, 0x3F, 0xF8, 0x20, 0x08, 0x20, 0x08,
        0x20, 0x08, 0x20, 0x08, 0x20, 0x08, 0x00, 0x00
    ],
    
    # "世" 字
    "世": [
        0x00, 0x00, 0x00, 0x00, 0x04, 0x40, 0x04, 0x40,
        0x04, 0x40, 0x04, 0x40, 0x04, 0x40, 0xFF, 0xFE,
        0x04, 0x40, 0x04, 0x40, 0x04, 0x40, 0x04, 0x40,
        0x04, 0x40, 0x04, 0x40, 0x04, 0x40, 0x00, 0x00
    ],
    
    # "界" 字
    "界": [
        0x00, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x10, 0x10,
        0x10, 0x10, 0x1F, 0xF0, 0x10, 0x10, 0x10, 0x10,
        0x1F, 0xF0, 0x04, 0x40, 0x04, 0x40, 0xFF, 0xFE,
        0x04, 0x40, 0x04, 0x40, 0x04, 0x40, 0x00, 0x00
    ],
    
    # "测" 字
    "测": [
        0x00, 0x00, 0x00, 0x00, 0x40, 0x04, 0x40, 0x04,
        0x40, 0x04, 0x7F, 0xFC, 0x40, 0x04, 0x40, 0x04,
        0x40, 0x04, 0x5F, 0xF4, 0x50, 0x14, 0x50, 0x14,
        0x50, 0x14, 0x5F, 0xF4, 0x50, 0x04, 0x00, 0x00
    ],
    
    # "试" 字
    "试": [
        0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x47, 0xF8,
        0x44, 0x08, 0x44, 0x08, 0x47, 0xF8, 0x44, 0x08,
        0x44, 0x08, 0x47, 0xF8, 0x40, 0x08, 0x40, 0x08,
        0x40, 0x08, 0x7F, 0xFE, 0x40, 0x00, 0x00, 0x00
    ],
    
    # "显" 字
    "显": [
        0x00, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x10, 0x10,
        0x10, 0x10, 0x1F, 0xF0, 0x10, 0x10, 0x10, 0x10,
        0x1F, 0xF0, 0x01, 0x00, 0x01, 0x00, 0xFF, 0xFE,
        0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00
    ],
    
    # "示" 字
    "示": [
        0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x80,
        0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0xFF, 0xFE,
        0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80,
        0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00
    ],
    
    # "中" 字
    "中": [
        0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x3F, 0xF8, 0x21, 0x08,
        0x21, 0x08, 0x21, 0x08, 0x3F, 0xF8, 0x01, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00
    ],
    
    # "文" 字
    "文": [
        0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x01, 0x00,
        0x01, 0x00, 0xFF, 0xFE, 0x01, 0x00, 0x01, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00
    ]
}

def get_chinese_char_data(char, font_size=16):
    """获取中文字符的点阵数据"""
    if font_size == 16:
        return CHINESE_FONT_16x16.get(char)
    else:
        return None

def get_supported_chars(font_size=16):
    """获取支持的中文字符列表"""
    if font_size == 16:
        return list(CHINESE_FONT_16x16.keys())
    else:
        return []

def print_supported_chars():
    """打印所有支持的中文字符"""
    print("支持的中文字符:")
    chars = get_supported_chars(16)
    for char in chars:
        print(char, end=' ')
    print()

if __name__ == "__main__":
    print("简化版中文字体库")
    print_supported_chars()

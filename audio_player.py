"""
音频播放器库 - YD-RP2040
支持I2S音频输出和音乐生成
"""

import machine
from machine import I2S
import math
import struct
import time

class AudioPlayer:
    def __init__(self):
        """初始化音频播放器"""
        self.audio_out = None
        self.is_playing = False
        self.current_song = 0
        self.volume = 0.5
        self.sample_rate = 22050
        
        # 音符频率表
        self.note_frequencies = {
            'C4': 261.63, 'C#4': 277.18, 'D4': 293.66, 'D#4': 311.13,
            'E4': 329.63, 'F4': 349.23, 'F#4': 369.99, 'G4': 392.00,
            'G#4': 415.30, 'A4': 440.00, 'A#4': 466.16, 'B4': 493.88,
            'C5': 523.25, 'C#5': 554.37, 'D5': 587.33, 'D#5': 622.25,
            'E5': 659.25, 'F5': 698.46, 'F#5': 739.99, 'G5': 783.99,
            'G#5': 830.61, 'A5': 880.00, 'A#5': 932.33, 'B5': 987.77,
            'C6': 1046.50, 'REST': 0  # 休止符
        }
        
        # 预设歌曲库
        self.songs = {
            "小星星": [
                ('C4', 0.5), ('C4', 0.5), ('G4', 0.5), ('G4', 0.5),
                ('A4', 0.5), ('A4', 0.5), ('G4', 1.0),
                ('F4', 0.5), ('F4', 0.5), ('E4', 0.5), ('E4', 0.5),
                ('D4', 0.5), ('D4', 0.5), ('C4', 1.0)
            ],
            "生日快乐": [
                ('C4', 0.75), ('C4', 0.25), ('D4', 1.0), ('C4', 1.0),
                ('F4', 1.0), ('E4', 2.0),
                ('C4', 0.75), ('C4', 0.25), ('D4', 1.0), ('C4', 1.0),
                ('G4', 1.0), ('F4', 2.0),
                ('C4', 0.75), ('C4', 0.25), ('C5', 1.0), ('A4', 1.0),
                ('F4', 1.0), ('E4', 1.0), ('D4', 2.0)
            ],
            "欢乐颂": [
                ('E4', 0.5), ('E4', 0.5), ('F4', 0.5), ('G4', 0.5),
                ('G4', 0.5), ('F4', 0.5), ('E4', 0.5), ('D4', 0.5),
                ('C4', 0.5), ('C4', 0.5), ('D4', 0.5), ('E4', 0.5),
                ('E4', 0.75), ('D4', 0.25), ('D4', 1.0)
            ],
            "两只老虎": [
                ('C4', 0.5), ('D4', 0.5), ('E4', 0.5), ('C4', 0.5),
                ('C4', 0.5), ('D4', 0.5), ('E4', 0.5), ('C4', 0.5),
                ('E4', 0.5), ('F4', 0.5), ('G4', 1.0),
                ('E4', 0.5), ('F4', 0.5), ('G4', 1.0)
            ]
        }
        
        self.song_names = list(self.songs.keys())
        
    def init_i2s(self):
        """初始化I2S音频输出"""
        try:
            self.audio_out = I2S(
                0,                                  # I2S ID
                sck=machine.Pin(11),               # 串行时钟
                ws=machine.Pin(12),                # 字选择
                sd=machine.Pin(10),                # 串行数据
                mode=I2S.TX,                       # 发送模式
                bits=16,                           # 16位音频
                format=I2S.STEREO,                 # 立体声
                rate=self.sample_rate,             # 采样率
                ibuf=2048                          # 缓冲区
            )
            print("✓ I2S音频初始化成功")
            return True
        except Exception as e:
            print(f"✗ I2S初始化失败: {e}")
            return False
    
    def generate_tone(self, frequency, duration, amplitude=None):
        """生成音调数据"""
        if amplitude is None:
            amplitude = self.volume
            
        samples = int(self.sample_rate * duration)
        audio_data = bytearray()
        
        for i in range(samples):
            if frequency == 0:  # 休止符
                sample = 0
            else:
                # 生成正弦波
                sample = amplitude * math.sin(2 * math.pi * frequency * i / self.sample_rate)
            
            # 转换为16位整数
            sample_int = int(sample * 32767)
            # 立体声（左右声道相同）
            audio_data.extend(struct.pack('<hh', sample_int, sample_int))
        
        return audio_data
    
    def play_note(self, note, duration):
        """播放单个音符"""
        if note in self.note_frequencies:
            frequency = self.note_frequencies[note]
            tone_data = self.generate_tone(frequency, duration)
            if self.audio_out:
                self.audio_out.write(tone_data)
    
    def play_song(self, song_name):
        """播放歌曲"""
        if song_name not in self.songs:
            print(f"歌曲 '{song_name}' 不存在")
            return False
        
        if not self.audio_out:
            print("音频输出未初始化")
            return False
        
        print(f"🎵 正在播放: {song_name}")
        self.is_playing = True
        
        try:
            notes = self.songs[song_name]
            for note, duration in notes:
                if not self.is_playing:  # 检查是否被停止
                    break
                self.play_note(note, duration)
                time.sleep(0.05)  # 音符间隔
            
            print(f"✓ 播放完成: {song_name}")
            self.is_playing = False
            return True
            
        except Exception as e:
            print(f"✗ 播放错误: {e}")
            self.is_playing = False
            return False
    
    def stop(self):
        """停止播放"""
        self.is_playing = False
        print("⏹️ 停止播放")
    
    def set_volume(self, volume):
        """设置音量 (0.0 - 1.0)"""
        self.volume = max(0.0, min(1.0, volume))
        print(f"🔊 音量设置为: {int(self.volume * 100)}%")
    
    def get_song_list(self):
        """获取歌曲列表"""
        return self.song_names
    
    def get_current_song_name(self):
        """获取当前歌曲名称"""
        if 0 <= self.current_song < len(self.song_names):
            return self.song_names[self.current_song]
        return "无歌曲"
    
    def next_song(self):
        """下一首歌"""
        self.current_song = (self.current_song + 1) % len(self.song_names)
        return self.get_current_song_name()
    
    def prev_song(self):
        """上一首歌"""
        self.current_song = (self.current_song - 1) % len(self.song_names)
        return self.get_current_song_name()
    
    def play_current_song(self):
        """播放当前歌曲"""
        current_name = self.get_current_song_name()
        return self.play_song(current_name)
    
    def play_demo_sound(self):
        """播放演示音效"""
        print("🎵 播放演示音效...")
        
        # 播放音阶
        scale = ['C4', 'D4', 'E4', 'F4', 'G4', 'A4', 'B4', 'C5']
        for note in scale:
            self.play_note(note, 0.3)
        
        print("✓ 演示音效播放完成")

# 使用示例
if __name__ == "__main__":
    # 创建播放器
    player = AudioPlayer()
    
    # 初始化音频
    if player.init_i2s():
        # 播放演示
        player.play_demo_sound()
        
        # 播放歌曲
        player.play_song("小星星")
    else:
        print("音频初始化失败，请检查硬件连接")

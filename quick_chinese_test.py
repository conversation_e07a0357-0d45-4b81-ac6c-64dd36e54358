"""
YD-RP2040 中文OLED快速测试
最简单的中文显示测试程序
"""

import machine
import time

def quick_test():
    """快速测试中文显示"""
    print("YD-RP2040 中文OLED快速测试")
    print("=" * 30)
    
    # 1. 初始化I2C
    print("1. 初始化I2C...")
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    
    # 2. 扫描设备
    print("2. 扫描I2C设备...")
    devices = i2c.scan()
    if devices:
        print(f"✓ 发现设备: {[hex(d) for d in devices]}")
    else:
        print("✗ 未发现设备，请检查连接！")
        print("连接方式:")
        print("OLED VCC → 3.3V")
        print("OLED GND → GND")
        print("OLED SCL → GP21")
        print("OLED SDA → GP20")
        return False
    
    # 3. 初始化中文OLED
    print("3. 初始化中文OLED...")
    try:
        from oled_chinese import ChineseOLED
        oled = ChineseOLED(128, 64, i2c)
        print("✓ 中文OLED初始化成功")
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return False
    
    # 4. 显示测试
    print("4. 中文显示测试...")
    
    # 清屏
    oled.fill(0)
    oled.show()
    time.sleep(0.5)
    
    # 显示中文
    oled.chinese_text("你好世界", 0, 0)
    oled.chinese_text("YD-RP2040", 0, 20)
    oled.chinese_text("中文显示", 0, 40)
    oled.show()
    
    print("✓ 如果屏幕显示中文，说明测试成功！")
    
    # 5. 动态演示
    print("5. 动态演示...")
    time.sleep(2)
    
    for i in range(5):
        oled.fill(0)
        oled.chinese_text_center("测试成功", 10)
        oled.chinese_text_center(f"计数: {i+1}", 30)
        oled.show()
        time.sleep(1)
    
    print("✓ 测试完成！")
    return True

if __name__ == "__main__":
    quick_test()

"""
音乐播放器主程序 - YD-RP2040
整合音频播放、显示和控制功能
"""

import time
import _thread
from audio_player import AudioPlayer
from music_display import MusicDisplay
from music_controller import MusicController

class MusicPlayerSystem:
    def __init__(self):
        """初始化音乐播放器系统"""
        self.audio_player = AudioPlayer()
        self.display = MusicDisplay()
        self.controller = MusicController()
        
        # 系统状态
        self.is_playing = False
        self.current_song_index = 0
        self.volume = 50
        self.song_list = []
        self.system_running = True
        
        # 播放线程控制
        self.play_thread_running = False
        
        print("🎵 音乐播放器系统初始化...")
    
    def init_system(self):
        """初始化所有子系统"""
        success = True
        
        # 初始化显示
        if not self.display.init_display():
            print("❌ 显示器初始化失败")
            success = False
        
        # 显示启动画面
        if success:
            self.display.show_startup_screen()
            time.sleep(2)
        
        # 初始化音频
        if not self.audio_player.init_i2s():
            print("❌ 音频系统初始化失败")
            success = False
        
        # 获取歌曲列表
        self.song_list = self.audio_player.get_song_list()
        self.audio_player.set_volume(self.volume / 100.0)
        
        if success:
            print("✅ 系统初始化完成")
            self.update_display()
        
        return success
    
    def update_display(self):
        """更新显示内容"""
        current_song = ""
        if self.song_list and 0 <= self.current_song_index < len(self.song_list):
            current_song = self.song_list[self.current_song_index]
        
        self.display.update_display(
            song_name=current_song,
            playing=self.is_playing,
            volume=self.volume,
            song_idx=self.current_song_index,
            total=len(self.song_list),
            progress=0  # 简化版本，不显示进度
        )
    
    def play_current_song(self):
        """在新线程中播放当前歌曲"""
        def play_thread():
            self.play_thread_running = True
            try:
                current_song = self.song_list[self.current_song_index]
                self.audio_player.current_song = self.current_song_index
                self.audio_player.play_song(current_song)
            except Exception as e:
                print(f"播放错误: {e}")
            finally:
                self.is_playing = False
                self.play_thread_running = False
                self.controller.set_led_status("paused")
                self.update_display()
        
        if not self.play_thread_running and self.song_list:
            self.is_playing = True
            self.controller.set_led_status("playing")
            _thread.start_new_thread(play_thread, ())
            self.update_display()
    
    def stop_current_song(self):
        """停止当前歌曲"""
        self.audio_player.stop()
        self.is_playing = False
        self.controller.set_led_status("paused")
        self.update_display()
    
    def next_song(self):
        """下一首歌"""
        if self.song_list:
            self.stop_current_song()
            self.current_song_index = (self.current_song_index + 1) % len(self.song_list)
            self.update_display()
            print(f"🎵 切换到: {self.song_list[self.current_song_index]}")
    
    def prev_song(self):
        """上一首歌"""
        if self.song_list:
            self.stop_current_song()
            self.current_song_index = (self.current_song_index - 1) % len(self.song_list)
            self.update_display()
            print(f"🎵 切换到: {self.song_list[self.current_song_index]}")
    
    def toggle_play_pause(self):
        """切换播放/暂停"""
        if self.is_playing:
            self.stop_current_song()
            print("⏸️ 暂停播放")
        else:
            self.play_current_song()
            print("▶️ 开始播放")
    
    def adjust_volume(self, direction):
        """调节音量"""
        if direction > 0:
            self.volume = min(100, self.volume + 5)
        else:
            self.volume = max(0, self.volume - 5)
        
        self.audio_player.set_volume(self.volume / 100.0)
        
        # 显示音量调节界面
        self.display.show_volume_adjust(self.volume)
        time.sleep(1)  # 显示1秒
        self.update_display()  # 返回主界面
    
    def handle_commands(self, commands):
        """处理控制命令"""
        for command in commands:
            if command == "next_song":
                if self.controller.get_control_mode() == "SONG":
                    self.next_song()
                elif self.controller.get_control_mode() == "VOLUME":
                    self.adjust_volume(1)
            
            elif command == "prev_song":
                if self.controller.get_control_mode() == "SONG":
                    self.prev_song()
                elif self.controller.get_control_mode() == "VOLUME":
                    self.adjust_volume(-1)
            
            elif command == "play_pause":
                self.toggle_play_pause()
            
            elif command == "volume_up":
                self.adjust_volume(1)
            
            elif command == "volume_down":
                self.adjust_volume(-1)
            
            elif command == "mode_changed":
                # 模式切换时更新显示
                mode = self.controller.get_control_mode()
                if mode == "VOLUME":
                    self.display.show_volume_adjust(self.volume)
                else:
                    self.update_display()
    
    def run(self):
        """运行主循环"""
        print("🎵 音乐播放器启动")
        print("控制说明:")
        print("- 旋转编码器: 切换歌曲/调节音量")
        print("- 按下编码器: 播放/暂停")
        print("- 菜单按钮: 切换控制模式")
        print("- 按Ctrl+C退出")
        
        try:
            while self.system_running:
                # 处理用户输入
                commands = self.controller.process_input()
                if commands:
                    self.handle_commands(commands)
                
                # 检查播放状态
                if self.is_playing and not self.play_thread_running:
                    # 歌曲播放完成，自动播放下一首
                    self.next_song()
                    time.sleep(0.5)  # 短暂延迟
                    self.play_current_song()
                
                time.sleep(0.05)  # 50ms主循环间隔
        
        except KeyboardInterrupt:
            print("\n🛑 正在退出音乐播放器...")
            self.shutdown()
    
    def shutdown(self):
        """关闭系统"""
        self.system_running = False
        self.stop_current_song()
        self.controller.status_led.off()
        
        # 显示退出信息
        if self.display.oled:
            self.display.clear()
            self.display.oled.text('Goodbye!', 40, 25)
            self.display.show()
        
        print("✅ 音乐播放器已关闭")

# 简化版本（无需额外硬件）
class SimpleMusicPlayer:
    """简化版音乐播放器 - 只需要OLED和基础功能"""
    def __init__(self):
        self.audio_player = AudioPlayer()
        self.display = MusicDisplay()
        self.current_song = 0
        self.song_list = []
        self.is_playing = False
    
    def init_and_run(self):
        """初始化并运行简化版播放器"""
        print("🎵 简化版音乐播放器")
        
        # 初始化显示
        if not self.display.init_display():
            print("显示器初始化失败")
            return
        
        # 初始化音频
        if not self.audio_player.init_i2s():
            print("音频初始化失败，请检查硬件连接")
            return
        
        self.song_list = self.audio_player.get_song_list()
        
        # 显示歌曲列表
        self.display.show_menu(self.song_list, 0)
        time.sleep(3)
        
        # 自动播放演示
        for i, song in enumerate(self.song_list):
            print(f"🎵 正在播放: {song}")
            
            # 更新显示
            self.display.update_display(
                song_name=song,
                playing=True,
                volume=50,
                song_idx=i,
                total=len(self.song_list)
            )
            
            # 播放歌曲
            self.audio_player.play_song(song)
            
            # 播放间隔
            time.sleep(2)
        
        print("✅ 演示完成")

def main():
    """主函数"""
    print("选择运行模式:")
    print("1. 完整版 (需要旋转编码器和按钮)")
    print("2. 简化版 (只需要OLED和音频模块)")
    
    # 这里简化为直接运行简化版
    print("运行简化版...")
    
    simple_player = SimpleMusicPlayer()
    simple_player.init_and_run()

if __name__ == "__main__":
    main()

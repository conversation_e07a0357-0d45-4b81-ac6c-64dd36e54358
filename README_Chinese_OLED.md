# YD-RP2040 中文OLED显示指南

本指南介绍如何在YD-RP2040开发板上使用0.96寸OLED屏幕显示中文字符。

## 硬件连接

### OLED屏幕连接
```
OLED模块    YD-RP2040
VCC    →    3.3V
GND    →    GND
SCL    →    GP21
SDA    →    GP20
```

### 支持的OLED规格
- 尺寸: 0.96寸
- 分辨率: 128x64像素
- 驱动芯片: SSD1306
- 接口: I2C
- I2C地址: 0x3C (默认)

## 文件说明

### 核心文件
1. **chinese_font.py** - 中文字体库
   - 包含常用中文字符的点阵数据
   - 支持16x16和12x12两种字体大小
   - 可扩展添加更多字符

2. **oled_chinese.py** - 中文OLED驱动
   - 基于标准SSD1306驱动扩展
   - 提供中文字符显示功能
   - 支持中英文混合显示

3. **test_chinese_display.py** - 完整测试程序
   - 包含所有功能的测试用例
   - 硬件连接检查
   - 各种显示模式测试

4. **chinese_display_demo.py** - 简单演示程序
   - 基本使用示例
   - 适合快速验证功能

## 快速开始

### 1. 上传文件到Pico
将以下文件上传到YD-RP2040:
- `ssd1306.py` (标准SSD1306驱动)
- `chinese_font.py` (中文字体库)
- `oled_chinese.py` (中文OLED驱动)
- `chinese_display_demo.py` (演示程序)

### 2. 运行演示
```python
# 在Thonny或其他MicroPython IDE中运行
exec(open('chinese_display_demo.py').read())
```

### 3. 基本使用代码
```python
import machine
from oled_chinese import ChineseOLED

# 初始化I2C和OLED
i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
oled = ChineseOLED(128, 64, i2c)

# 显示中文
oled.fill(0)
oled.chinese_text("你好世界", 0, 0)
oled.chinese_text("YD-RP2040", 0, 20)
oled.show()
```

## 功能介绍

### 支持的中文字符
当前字体库包含以下常用字符：
```
你 好 世 界 测 试 显 示 中 文 字 体
温 度 湿 压 力 时 间
```

### 主要功能

#### 1. 基本中文显示
```python
# 显示单个中文字符
oled.draw_chinese_char("你", 0, 0)

# 显示中文文本
oled.chinese_text("你好世界", 0, 0)
```

#### 2. 居中显示
```python
# 文本居中显示
oled.chinese_text_center("中文测试", 20)
```

#### 3. 中英文混合
```python
# 中英文混合显示
oled.chinese_text("Hello 世界", 0, 0)
oled.chinese_text("Temperature 温度", 0, 20)
```

#### 4. 字体大小设置
```python
# 设置16x16字体 (默认)
oled.set_chinese_font_size(16)

# 设置12x12字体 (更小，可显示更多内容)
oled.set_chinese_font_size(12)
```

#### 5. 多行显示
```python
# 多行文本显示
text = "第一行\n第二行\n第三行"
oled.chinese_text_multiline(text, 0, 0)
```

## 应用示例

### 1. 环境监测显示
```python
def show_sensor_data(temp, humidity):
    oled.fill(0)
    oled.chinese_text_center("环境监测", 0)
    oled.hline(0, 16, 128, 1)
    oled.chinese_text(f"温度: {temp}°C", 10, 22)
    oled.chinese_text(f"湿度: {humidity}%", 10, 38)
    oled.show()
```

### 2. 菜单系统
```python
def show_menu(items, selected):
    oled.fill(0)
    oled.chinese_text_center("系统菜单", 0)
    oled.hline(0, 16, 128, 1)
    
    for i, item in enumerate(items):
        prefix = "> " if i == selected else "  "
        oled.chinese_text(f"{prefix}{item}", 0, 20 + i*12)
    oled.show()
```

### 3. 状态显示
```python
def show_status(status, time_str):
    oled.fill(0)
    oled.chinese_text_center("设备状态", 0)
    oled.chinese_text_center(status, 20)
    oled.chinese_text_center(time_str, 40)
    oled.show()
```

## 扩展字体库

### 添加新字符
如果需要显示更多中文字符，可以扩展字体库：

1. **获取字符点阵数据**
   - 使用字体工具生成16x16或12x12点阵
   - 转换为字节数组格式

2. **添加到字体库**
```python
from chinese_font import add_chinese_char

# 添加新字符 (示例数据)
new_char_data = [0x00, 0x00, 0x10, 0x40, ...]  # 32字节的点阵数据
add_chinese_char("新", new_char_data, 16)
```

### 字体工具推荐
- PCtoLCD2002 (Windows)
- 字模提取软件
- 在线字体生成器

## 故障排除

### 常见问题

1. **屏幕无显示**
   - 检查硬件连接
   - 确认I2C地址 (通常是0x3C)
   - 检查电源供应

2. **中文字符显示为方框**
   - 字符不在字体库中
   - 检查字符编码

3. **显示位置错误**
   - 检查坐标参数
   - 确认屏幕分辨率设置

### 调试代码
```python
# I2C设备扫描
i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
devices = i2c.scan()
print(f"I2C设备: {[hex(d) for d in devices]}")

# 检查支持的字符
from chinese_font import get_supported_chars
print("支持的字符:", get_supported_chars(16))
```

## 性能优化

### 显示优化建议
1. **减少刷新频率** - 避免过于频繁的`show()`调用
2. **使用小字体** - 12x12字体比16x16更节省空间
3. **局部更新** - 只更新变化的区域
4. **缓存显示** - 避免重复绘制相同内容

### 内存优化
```python
# 定期清理内存
import gc
gc.collect()

# 检查内存使用
print(f"可用内存: {gc.mem_free()} 字节")
```

## 许可证

本项目基于MIT许可证开源，可自由使用和修改。

## 贡献

欢迎提交问题报告和改进建议！如果您添加了新的中文字符或改进了代码，欢迎提交贡献。

"""
YD-RP2040 中文OLED显示测试
测试0.96寸OLED屏幕显示中文字符
"""

import machine
import time
from oled_chinese import ChineseOLED
from chinese_font import get_supported_chars, print_supported_chars

def hardware_check():
    """硬件连接检查"""
    print("=== 硬件连接检查 ===")
    print("请确认以下连接:")
    print("OLED VCC → YD-RP2040 3.3V")
    print("OLED GND → YD-RP2040 GND") 
    print("OLED SCL → YD-RP2040 GP21")
    print("OLED SDA → YD-RP2040 GP20")
    print()
    
    # 配置I2C
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    
    # 扫描I2C设备
    print("扫描I2C设备...")
    devices = i2c.scan()
    
    if devices:
        print(f"✓ 发现I2C设备: {[hex(device) for device in devices]}")
        return i2c, devices[0]  # 返回第一个设备地址
    else:
        print("✗ 未发现I2C设备，请检查连接！")
        return None, None

def test_basic_chinese():
    """基础中文显示测试"""
    print("\n=== 基础中文显示测试 ===")
    
    i2c, addr = hardware_check()
    if not i2c:
        return False
    
    try:
        # 初始化中文OLED
        oled = ChineseOLED(128, 64, i2c, addr)
        print("✓ 中文OLED初始化成功")
        
        # 测试1: 单个中文字符
        print("测试1: 单个中文字符...")
        oled.fill(0)
        oled.draw_chinese_char("你", 0, 0)
        oled.draw_chinese_char("好", 20, 0)
        oled.draw_chinese_char("世", 40, 0)
        oled.draw_chinese_char("界", 60, 0)
        oled.show()
        time.sleep(2)
        
        # 测试2: 中文文本
        print("测试2: 中文文本...")
        oled.fill(0)
        oled.chinese_text("你好世界", 0, 0)
        oled.chinese_text("中文显示", 0, 20)
        oled.chinese_text("测试成功", 0, 40)
        oled.show()
        time.sleep(2)
        
        # 测试3: 居中显示
        print("测试3: 居中显示...")
        oled.fill(0)
        oled.chinese_text_center("中文测试", 10)
        oled.chinese_text_center("YD-RP2040", 30)
        oled.chinese_text_center("OLED显示", 50)
        oled.show()
        time.sleep(2)
        
        print("✓ 基础中文显示测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_mixed_text():
    """中英文混合显示测试"""
    print("\n=== 中英文混合显示测试 ===")
    
    i2c, addr = hardware_check()
    if not i2c:
        return False
    
    try:
        oled = ChineseOLED(128, 64, i2c, addr)
        
        # 混合文本测试
        test_texts = [
            "Hello 世界",
            "YD-RP2040 中文",
            "OLED 显示器",
            "Temperature 温度",
            "Humidity 湿度"
        ]
        
        for i, text in enumerate(test_texts):
            print(f"显示: {text}")
            oled.fill(0)
            oled.chinese_text_center(text, 20)
            oled.text(f"Test {i+1}/5", 0, 50)
            oled.show()
            time.sleep(1.5)
        
        print("✓ 中英文混合显示测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_font_sizes():
    """字体大小测试"""
    print("\n=== 字体大小测试 ===")
    
    i2c, addr = hardware_check()
    if not i2c:
        return False
    
    try:
        oled = ChineseOLED(128, 64, i2c, addr)
        
        # 测试16x16字体
        print("测试16x16字体...")
        oled.set_chinese_font_size(16)
        oled.fill(0)
        oled.chinese_text("16x16", 0, 0)
        oled.chinese_text("大字体", 0, 20)
        oled.show()
        time.sleep(2)
        
        # 测试12x12字体
        print("测试12x12字体...")
        oled.set_chinese_font_size(12)
        oled.fill(0)
        oled.chinese_text("12x12", 0, 0)
        oled.chinese_text("小字体", 0, 15)
        oled.chinese_text("更多内容", 0, 30)
        oled.chinese_text("可以显示", 0, 45)
        oled.show()
        time.sleep(2)
        
        print("✓ 字体大小测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_dynamic_display():
    """动态显示测试"""
    print("\n=== 动态显示测试 ===")
    
    i2c, addr = hardware_check()
    if not i2c:
        return False
    
    try:
        oled = ChineseOLED(128, 64, i2c, addr)
        oled.set_chinese_font_size(16)
        
        # 动态计数显示
        print("动态计数显示...")
        for i in range(10):
            oled.fill(0)
            oled.chinese_text_center("计数测试", 0)
            oled.chinese_text_center(f"数字: {i}", 20)
            oled.chinese_text_center(f"时间: {time.time():.0f}s", 40)
            oled.show()
            time.sleep(0.5)
        
        # 滚动文本
        print("滚动文本显示...")
        text = "你好世界中文显示测试"
        for offset in range(len(text) * 16):
            oled.fill(0)
            oled.chinese_text(-offset, 20, text)
            oled.show()
            time.sleep(0.1)
        
        print("✓ 动态显示测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_sensor_display():
    """传感器数据显示模拟"""
    print("\n=== 传感器数据显示模拟 ===")
    
    i2c, addr = hardware_check()
    if not i2c:
        return False
    
    try:
        oled = ChineseOLED(128, 64, i2c, addr)
        oled.set_chinese_font_size(12)
        
        # 模拟传感器数据
        import random
        
        for i in range(10):
            # 生成模拟数据
            temp = 20 + random.randint(0, 10)
            humidity = 50 + random.randint(0, 30)
            pressure = 1000 + random.randint(0, 50)
            
            oled.fill(0)
            
            # 标题
            oled.chinese_text_center("环境监测", 0)
            oled.hline(0, 12, 128, 1)
            
            # 数据显示
            oled.chinese_text(f"温度: {temp}°C", 0, 16)
            oled.chinese_text(f"湿度: {humidity}%", 0, 28)
            oled.chinese_text(f"压力: {pressure}Pa", 0, 40)
            oled.chinese_text(f"时间: {i+1:02d}:00", 0, 52)
            
            oled.show()
            time.sleep(1)
        
        print("✓ 传感器数据显示测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def show_supported_characters():
    """显示支持的字符"""
    print("\n=== 支持的中文字符 ===")
    print_supported_chars()
    
    i2c, addr = hardware_check()
    if not i2c:
        return False
    
    try:
        oled = ChineseOLED(128, 64, i2c, addr)
        oled.set_chinese_font_size(16)
        
        # 显示所有支持的字符
        supported_chars = get_supported_chars(16)
        
        # 分页显示
        chars_per_page = 8  # 每页显示8个字符 (4x2)
        pages = len(supported_chars) // chars_per_page + 1
        
        for page in range(pages):
            start_idx = page * chars_per_page
            end_idx = min(start_idx + chars_per_page, len(supported_chars))
            page_chars = supported_chars[start_idx:end_idx]
            
            oled.fill(0)
            oled.text(f"Page {page+1}/{pages}", 0, 0)
            
            # 4x2布局显示字符
            for i, char in enumerate(page_chars):
                x = (i % 4) * 32
                y = 16 + (i // 4) * 20
                oled.draw_chinese_char(char, x, y)
            
            oled.show()
            time.sleep(2)
        
        print("✓ 字符显示完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("YD-RP2040 中文OLED显示测试")
    print("=" * 40)
    
    # 运行所有测试
    tests = [
        ("基础中文显示", test_basic_chinese),
        ("中英文混合显示", test_mixed_text),
        ("字体大小测试", test_font_sizes),
        ("动态显示测试", test_dynamic_display),
        ("传感器数据显示", test_sensor_display),
        ("支持字符显示", show_supported_characters)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n开始 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！中文显示功能正常！")
    else:
        print("⚠️  部分测试失败，请检查硬件连接和代码")

if __name__ == "__main__":
    main()
